import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useState, useEffect } from "react";

const Products = () => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovering, setIsHovering] = useState(false);
  const [email, setEmail] = useState("");

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      const heroSection = document.getElementById("products-hero-section");
      if (heroSection) {
        const rect = heroSection.getBoundingClientRect();
        setMousePosition({
          x: e.clientX - rect.left,
          y: e.clientY - rect.top,
        });
      }
    };

    const heroSection = document.getElementById("products-hero-section");
    if (heroSection) {
      heroSection.addEventListener("mousemove", handleMouseMove);
      return () =>
        heroSection.removeEventListener("mousemove", handleMouseMove);
    }
  }, []);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Email submitted:", email);
    // Handle form submission here
  };
  const products = [
    {
      name: "LumenCloud",
      description: "Enterprise cloud infrastructure platform",
      category: "Infrastructure",
      status: "Live",
    },
    {
      name: "LumenMail",
      description: "Professional email hosting service",
      category: "Communication",
      status: "Live",
    },
    {
      name: "LumenSecure",
      description: "Cybersecurity monitoring and response",
      category: "Security",
      status: "Live",
    },
    {
      name: "LumenFlow",
      description: "Business process automation platform",
      category: "Automation",
      status: "Beta",
    },
  ];

  const companies = [
    {
      name: "LumenWorks Digital",
      description: "Web development and digital marketing",
      focus: "Small to medium businesses",
    },
    {
      name: "LumenWorks Enterprise",
      description: "Enterprise cloud solutions and consulting",
      focus: "Large corporations and government",
    },
    {
      name: "LumenWorks Security",
      description: "Cybersecurity services and compliance",
      focus: "Regulated industries",
    },
  ];

  return (
    <div className="min-h-screen bg-white relative overflow-hidden">
      {/* Background gradient clouds */}
      <div className="fixed inset-0 pointer-events-none">
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-gradient-to-br from-lumen-yellow/10 to-white/50 rounded-full blur-3xl transform -translate-y-1/2"></div>
        <div className="absolute top-1/3 right-1/4 w-80 h-80 bg-gradient-to-bl from-lumen-yellow/15 to-lumen-yellow/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 left-1/3 w-72 h-72 bg-gradient-to-tr from-lumen-yellow/8 to-white/30 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10">
        <Header />
        <main className="pt-16 lg:pt-20">
          <section className="w-full bg-lumen-yellow py-20 lg:py-28 pt-28 relative overflow-hidden">
            <div className="max-w-content mx-auto px-6">
              <div className="text-center mb-8">
                <h2 className="text-4xl lg:text-5xl font-bold text-lumen-charcoal leading-tight xl:text-6xl mb-4">
                  Products & companies
                </h2>
                <p className="text-lg text-lumen-charcoal/80 max-w-2xl mx-auto">
                  A comprehensive ecosystem of technology solutions and
                  specialized companies designed to transform your business.
                </p>
              </div>
            </div>
          </section>

          <div className="py-20 lg:py-28">
            <div className="max-w-content mx-auto px-6">
              {/* Products Section */}
              <div className="mb-20">
                <h2 className="text-3xl font-bold text-lumen-charcoal mb-12 text-center">
                  Products
                </h2>
                <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
                  {products.map((product, index) => (
                    <div
                      key={index}
                      className="bg-white rounded-3xl p-8 border border-lumen-yellow/20 shadow-lg hover:shadow-2xl transition-all duration-300 hover:-translate-y-2"
                    >
                      <div className="flex justify-between items-start mb-4">
                        <h3 className="text-xl font-bold text-lumen-charcoal">
                          {product.name}
                        </h3>
                        <span
                          className={`px-3 py-1 rounded-full text-xs font-medium ${
                            product.status === "Live"
                              ? "bg-green-100 text-green-800"
                              : "bg-yellow-100 text-yellow-800"
                          }`}
                        >
                          {product.status}
                        </span>
                      </div>
                      <p className="text-sm text-lumen-mid-gray mb-4">
                        {product.description}
                      </p>
                      <span className="text-xs text-lumen-charcoal bg-lumen-yellow/20 px-2 py-1 rounded">
                        {product.category}
                      </span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Companies Section */}
              <div>
                <h2 className="text-3xl font-bold text-lumen-charcoal mb-12 text-center">
                  Companies
                </h2>
                <div className="grid md:grid-cols-3 gap-8">
                  {companies.map((company, index) => (
                    <div
                      key={index}
                      className="bg-gradient-to-br from-lumen-off-white to-lumen-yellow/10 rounded-3xl p-8"
                    >
                      <h3 className="text-xl font-bold text-lumen-charcoal mb-4">
                        {company.name}
                      </h3>
                      <p className="text-lumen-mid-gray mb-4">
                        {company.description}
                      </p>
                      <p className="text-sm font-medium text-lumen-charcoal">
                        Focus: {company.focus}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    </div>
  );
};

export default Products;
